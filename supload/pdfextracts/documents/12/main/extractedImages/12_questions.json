{"questions": [{"question_number": "1", "question": "\\(\\frac{5y}{1+\\frac{1}{1+\\frac{y}{1-y}}} = 3\\), तो \\(y = ?\\)", "option1": "\\(\\frac{4}{5}\\)", "option2": "\\(\\frac{3}{4}\\)", "option3": "\\(\\frac{5}{6}\\)", "option4": "\\(\\frac{3}{10}\\)", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "2", "question": "\\(\\frac{7y}{1+\\frac{1}{1+\\frac{y}{1-y}}} = 4\\), तो \\(y = ?\\)", "option1": "\\(\\frac{8}{11}\\)", "option2": "\\(\\frac{7}{15}\\)", "option3": "\\(\\frac{8}{15}\\)", "option4": "\\(\\frac{11}{15}\\)", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "3", "question": "\\(\\frac{11y}{1+\\frac{1}{1+\\frac{y}{1-y}}} = 13\\), तो \\(y = ?\\)", "option1": "\\(2 \\frac{5}{12}\\)", "option2": "\\(1 \\frac{7}{12}\\)", "option3": "\\(1 \\frac{1}{12}\\)", "option4": "\\(5 \\frac{5}{12}\\)", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "4", "question": "\\(\\frac{4}{(216)^{-\\frac{2}{3}}} + \\frac{1}{(256)^{-\\frac{3}{4}}} + \\frac{2}{(243)^{-\\frac{1}{5}}} = ?\\)", "option1": "36", "option2": "243", "option3": "216", "option4": "214", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "5", "question": "\\(\\sqrt{\\left(\\frac{3}{5}\\right)^{1-2x}} = 4 \\frac{17}{27}\\), तो \\(2x = ?\\)", "option1": "3.5", "option2": "7", "option3": "8", "option4": "10", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "6", "question": "\\(\\frac{3 \\times 27^{n+1} + 9 \\times 3^{3n-1}}{8 \\times 3^{3n} - 5 \\times 27^n} = ?\\)", "option1": "28", "option2": "29", "option3": "27", "option4": "30", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "7", "question": "750 में गुणनखंडों की कुल संख्या क्या है?", "option1": "12", "option2": "24", "option3": "20", "option4": "30", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "8", "question": "2250 में पूर्ण वर्ग गुणनखंडों की कुल संख्या क्या है?", "option1": "5", "option2": "4", "option3": "6", "option4": "8", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "9", "question": "32400 में कुल घन गुणनखंडों की संख्या क्या है?", "option1": "1", "option2": "2", "option3": "3", "option4": "4", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "10", "question": "9600 के ऐसे कितने घटक हैं जो 12 से पूर्णतः विभाज्य नहीं हैं?", "option1": "48", "option2": "30", "option3": "16", "option4": "32", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "11", "question": "1200 के गुणनखंडों का योग ज्ञात करें?", "option1": "3684", "option2": "3872", "option3": "3844", "option4": "7844", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "12", "question": "32400 के पूर्ण घन संख्या के गुणनखंडों का योग ज्ञात करें?", "option1": "252", "option2": "262", "option3": "248", "option4": "250", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "13", "question": "100 + 50 का (10 - 2) ÷ 4 - 150 = ?", "option1": "50", "option2": "250", "option3": "120", "option4": "80", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "14", "question": "20 1/2 ÷ 200 का [ 250 - {100} + (80 - 50 + 20) ] = ?", "option1": "41/2800", "option2": "41/5600", "option3": "51/5600", "option4": "41/56000", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "15", "question": "यदि n² = 12345678987654321, तो n = ?", "option1": "123456", "option2": "78951231", "option3": "56789111", "option4": "111111111", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "16", "question": "निम्न में से कौन-सा सत्य है?", "option1": "5² = (5³)²", "option2": "5² < (5³)²", "option3": "5^{3²} > (5³)²", "option4": "5² ≥ (5³)²", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "17", "question": "यदि P = Q + 1, जहाँ Q चार लगातार धनात्मक पूर्णांक संख्याओं का गुणनफल है। निम्न में से कौन-सा सत्य है?\n(i) P विषम है।\n(ii) P अभाज्य है।\n(iii) P पूर्ण वर्ग संख्या है।", "option1": "(i) तथा (iii)", "option2": "(i) तथा (ii)", "option3": "(i)", "option4": "इनमें से कोई नहीं", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "18", "question": "30^{2720} के अंत में दायें से बिना शून्य का अंतिम अंक कौन-सा है?", "option1": "3", "option2": "7", "option3": "9", "option4": "1", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": "19", "question": "n³ विषम अंक है तो निम्न में से कौन-सा सत्य है?\n(i) n विषम संख्या है।\n(ii) n² विषम संख्या है।\n(iii) n² सम संख्या है।", "option1": "सिर्फ (i)", "option2": "सिर्फ (i)", "option3": "सिर्फ (i) तथा (ii)", "option4": "सिर्फ (i) तथा (iii)", "question_type": "MCQ", "marks": "", "negative_mark:": "", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}], "total_questions": 19, "document_id": 12, "extraction_timestamp": "58901019-f37e-4575-a35e-2e5ff6ea22b9"}