package com.wonderslate.content

class ContentExamDtl {

    ContentExamMst exam
    Integer year
    String month
    String shift
    String questionPaperPath
    String extractedContent
    String createdBy
    Date dateCreated

    static constraints = {
        exam nullable: false
        year nullable: false
        month nullable: true
        shift nullable: true
        questionPaperPath blank: true, nullable: true
        extractedContent blank: true, nullable: true
    }

    static mapping = {
        table 'content_exam_dtl'
        exam column: 'content_exam_mst_id'
    }

    String toString() {
        "${exam?.examName} - ${year} ${month ?: ''} ${shift ?: ''}"
    }
}
