package com.wonderslate.content

class ContentExamSolutions {
    ContentExamDtl examDtl
    String question
    String questionType  // e.g., 'MCQ', 'Descriptive'
    String answer

    String option1
    String option2
    String option3
    String option4
    String option5

    BigDecimal marks
    BigDecimal negativeMark

    String topic
    String subtopic

    static constraints = {
        examDtl nullable: false
        question blank: false
        questionType inList: ['MCQ', 'Descriptive']
        answer blank: false

        option1 nullable: true
        option2 nullable: true
        option3 nullable: true
        option4 nullable: true
        option5 nullable: true

        marks nullable: false, scale: 2
        negativeMark nullable: false, scale: 2

        topic nullable: true
        subtopic nullable: true
    }

    static mapping = {
        table 'content_exam_solutions'
        examDtl column: 'content_exam_dtl_id'
    }

    String toString() {
        "Q: ${question?.take(50)}..."
    }
}
